# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flutter project named "openfitv2" - a fitness tracking application integrated with Supabase for backend services. The project is set up for cross-platform development (Android, iOS, Web, Linux, macOS, Windows) and includes user authentication and profile management.

## Development Commands

### Dependencies
- `flutter pub get` - Install dependencies
- `flutter pub upgrade` - Upgrade dependencies to latest versions
- `flutter pub outdated` - Check for outdated dependencies

### Development
- `flutter run` - Run the app in development mode with hot reload
- `flutter run -d chrome` - Run on web browser
- `flutter run -d android` - Run on Android device/emulator
- `flutter run -d ios` - Run on iOS device/simulator

### Build
- `flutter build apk` - Build Android APK
- `flutter build ios` - Build iOS app
- `flutter build web` - Build web app
- `flutter build windows` - Build Windows app
- `flutter build macos` - Build macOS app
- `flutter build linux` - Build Linux app

### Testing & Analysis
- `flutter test` - Run all tests
- `flutter test test/widget_test.dart` - Run specific test file
- `flutter analyze` - Run static analysis (configured via analysis_options.yaml)

### Maintenance
- `flutter clean` - Clean build artifacts
- `flutter doctor` - Check Flutter installation and dependencies

## Project Structure

- `lib/main.dart` - Main entry point with MyApp and MyHomePage widgets
- `test/` - Widget and unit tests
- `android/`, `ios/`, `web/`, `windows/`, `macos/`, `linux/` - Platform-specific code
- `pubspec.yaml` - Dependency configuration and project metadata
- `analysis_options.yaml` - Dart analyzer configuration using flutter_lints

## Architecture Notes

- **Authentication**: Supabase-based auth with email/password
- **State Management**: Provider pattern for auth state
- **Project Structure**:
  - `lib/config/` - Configuration files (Supabase credentials)
  - `lib/services/` - Business logic (AuthService)
  - `lib/screens/` - UI screens organized by feature
    - `auth/` - Login and signup screens
    - `home_screen.dart` - Main app screen after authentication
- **Database**: Supabase (PostgreSQL) with tables for:
  - `profiles` - User profiles with fitness preferences
  - `exercises`, `workouts`, `workout_sessions` - Fitness tracking
- Standard Flutter testing setup with widget_test.dart using flutter_test package
- Linting configured with flutter_lints package for code quality

## Development Environment

- Dart SDK: ^3.8.1
- **Key Dependencies**:
  - `supabase_flutter: ^2.8.1` - Backend integration
  - `provider: ^6.1.2` - State management
  - `flutter_secure_storage: ^9.2.2` - Secure credential storage
  - `email_validator: ^3.0.0` - Form validation
- Uses flutter_lints for code analysis
- Material Design 3 components with cupertino_icons for iOS-style icons

## Supabase Integration

The app connects to a Supabase project (SciWell Mobile) with:
- Authentication system with email verification
- User profiles table with extensive fitness tracking fields
- Exercise library and workout tracking tables
- Real-time capabilities (not yet implemented in Flutter app)