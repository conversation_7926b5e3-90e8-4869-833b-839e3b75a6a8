import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Standardized button sizes
enum AppButtonSize {
  small,
  medium,
  large,
}

/// Standardized button variants
enum AppButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  success,
  warning,
  error,
}

/// A standardized button component that follows the design system
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonSize size;
  final AppButtonVariant variant;
  final IconData? icon;
  final bool isLoading;
  final bool fullWidth;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.variant = AppButtonVariant.primary,
    this.icon,
    this.isLoading = false,
    this.fullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle();
    final textStyle = _getTextStyle();
    final padding = _getPadding();

    Widget buttonChild = Row(
      mainAxisSize: fullWidth ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading) ...[
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(textStyle.color!),
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
        ] else if (icon != null) ...[
          Icon(icon, size: _getIconSize()),
          const SizedBox(width: AppSpacing.sm),
        ],
        Text(text, style: textStyle),
      ],
    );

    if (variant == AppButtonVariant.outline) {
      return SizedBox(
        width: fullWidth ? double.infinity : null,
        child: OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: Padding(
            padding: padding,
            child: buttonChild,
          ),
        ),
      );
    } else if (variant == AppButtonVariant.ghost) {
      return SizedBox(
        width: fullWidth ? double.infinity : null,
        child: TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: Padding(
            padding: padding,
            child: buttonChild,
          ),
        ),
      );
    } else {
      return SizedBox(
        width: fullWidth ? double.infinity : null,
        child: ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: Padding(
            padding: padding,
            child: buttonChild,
          ),
        ),
      );
    }
  }

  ButtonStyle _getButtonStyle() {
    // Note: ButtonStyle doesn't have access to context, so we'll use static colors
    // The theme will be applied through the MaterialApp theme
    Color backgroundColor;
    Color foregroundColor;
    BorderSide? border;

    switch (variant) {
      case AppButtonVariant.primary:
        backgroundColor = AppColors.primary;
        foregroundColor = AppColors.textOnPrimary;
        break;
      case AppButtonVariant.secondary:
        backgroundColor = AppColors.grey100;
        foregroundColor = AppColors.lightTextPrimary; // Will be overridden by theme
        break;
      case AppButtonVariant.outline:
        backgroundColor = Colors.transparent;
        foregroundColor = AppColors.primary;
        border = const BorderSide(color: AppColors.primary);
        break;
      case AppButtonVariant.ghost:
        backgroundColor = Colors.transparent;
        foregroundColor = AppColors.primary;
        break;
      case AppButtonVariant.success:
        backgroundColor = AppColors.success;
        foregroundColor = AppColors.textOnPrimary;
        break;
      case AppButtonVariant.warning:
        backgroundColor = AppColors.warning;
        foregroundColor = AppColors.textOnPrimary;
        break;
      case AppButtonVariant.error:
        backgroundColor = AppColors.error;
        foregroundColor = AppColors.textOnPrimary;
        break;
    }

    return ButtonStyle(
      backgroundColor: WidgetStateProperty.all(backgroundColor),
      foregroundColor: WidgetStateProperty.all(foregroundColor),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: AppBorderRadius.buttonRadius,
        ),
      ),
      side: border != null ? WidgetStateProperty.all(border) : null,
      elevation: variant == AppButtonVariant.outline || variant == AppButtonVariant.ghost
          ? WidgetStateProperty.all(0)
          : null,
    );
  }

  TextStyle _getTextStyle() {
    Color color;
    switch (variant) {
      case AppButtonVariant.primary:
      case AppButtonVariant.success:
      case AppButtonVariant.warning:
      case AppButtonVariant.error:
        color = AppColors.textOnPrimary;
        break;
      case AppButtonVariant.secondary:
        color = AppColors.lightTextPrimary; // Will be overridden by theme
        break;
      case AppButtonVariant.outline:
      case AppButtonVariant.ghost:
        color = AppColors.primary;
        break;
    }

    switch (size) {
      case AppButtonSize.small:
        return AppTypography.buttonSmall.copyWith(color: color);
      case AppButtonSize.medium:
        return AppTypography.buttonMedium.copyWith(color: color);
      case AppButtonSize.large:
        return AppTypography.buttonLarge.copyWith(color: color);
    }
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(vertical: AppSpacing.sm, horizontal: AppSpacing.md);
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(vertical: AppSpacing.md, horizontal: AppSpacing.lg);
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(vertical: AppSpacing.lg, horizontal: AppSpacing.xl);
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return 16;
      case AppButtonSize.medium:
        return 18;
      case AppButtonSize.large:
        return 20;
    }
  }
}