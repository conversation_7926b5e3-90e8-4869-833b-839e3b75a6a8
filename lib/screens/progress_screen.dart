import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart';
import '../design_system/design_system.dart';


class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> {
  final SupabaseClient _supabase = Supabase.instance.client;
  List<Map<String, dynamic>> _completedWorkouts = [];
  bool _isLoading = true;
  int _totalWorkouts = 0;
  int _totalSets = 0;
  int _currentStreak = 0;

  @override
  void initState() {
    super.initState();
    _loadProgressData();
  }

  Future<void> _loadProgressData() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return;

      // Get completed workouts with set counts
      final workoutsResponse = await _supabase
          .from('workouts')
          .select('''
            id,
            name,
            created_at,
            start_time,
            end_time,
            duration,
            rating
          ''')
          .eq('user_id', user.id)
          .eq('is_completed', true)
          .order('created_at', ascending: false)
          .limit(10);

      // Get total sets count for completed workouts
      final setsResponse = await _supabase.rpc('get_user_completed_sets_count', 
          params: {'user_id_param': user.id});

      setState(() {
        _completedWorkouts = List<Map<String, dynamic>>.from(workoutsResponse);
        _totalWorkouts = _completedWorkouts.length;
        _totalSets = setsResponse ?? 0;
        _currentStreak = _calculateStreak();
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading progress data: $e');
      // Fallback to basic data without sets count
      try {
        final user = _supabase.auth.currentUser;
        if (user != null) {
          final workoutsResponse = await _supabase
              .from('workouts')
              .select('id, name, created_at, start_time, end_time, duration, rating')
              .eq('user_id', user.id)
              .eq('is_completed', true)
              .order('created_at', ascending: false)
              .limit(10);

          setState(() {
            _completedWorkouts = List<Map<String, dynamic>>.from(workoutsResponse);
            _totalWorkouts = _completedWorkouts.length;
            _totalSets = 0; // Will show 0 if we can't get sets count
            _currentStreak = _calculateStreak();
            _isLoading = false;
          });
        }
      } catch (e2) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  int _calculateStreak() {
    if (_completedWorkouts.isEmpty) return 0;
    
    // Simple streak calculation based on recent workouts
    int streak = 0;
    final now = DateTime.now();
    
    for (final workout in _completedWorkouts) {
      final workoutDate = DateTime.parse(workout['created_at']);
      final daysDiff = now.difference(workoutDate).inDays;
      
      if (daysDiff <= 7) {
        streak++;
      } else {
        break;
      }
    }
    
    return streak;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      body: SafeArea(
        child: _isLoading ? _buildLoadingState() : _buildContent(),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
      ),
    );
  }

  Widget _buildContent() {
    return RefreshIndicator(
      onRefresh: _loadProgressData,
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Column(
              children: [
                AppHeader(
                  title: 'Progress',
                  subtitle: 'Track your fitness journey and achievements',
                ),
                Padding(
                  padding: const EdgeInsets.all(AppSpacing.screenPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatsCards(),
                      const SizedBox(height: AppSpacing.sectionSpacing),
                      _buildRecentWorkoutsSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(
          child: AppStatCard(
            icon: Icons.fitness_center,
            iconColor: AppColors.primary,
            value: _totalWorkouts.toString(),
            label: 'Total Workouts',
          ),
        ),
        const SizedBox(width: AppSpacing.lg),
        Expanded(
          child: AppStatCard(
            icon: Icons.check_circle,
            iconColor: AppColors.success,
            value: _totalSets.toString(),
            label: 'Sets Completed',
          ),
        ),
        const SizedBox(width: AppSpacing.lg),
        Expanded(
          child: AppStatCard(
            icon: Icons.local_fire_department,
            iconColor: AppColors.warning,
            value: _currentStreak.toString(),
            label: 'Current Streak',
            subtitle: 'days',
          ),
        ),
      ],
    );
  }



  Widget _buildRecentWorkoutsSection() {
    if (_completedWorkouts.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Workouts',
          style: AppTypography.heading2,
        ),
        const SizedBox(height: AppSpacing.lg),
        ..._completedWorkouts.map((workout) => _buildWorkoutCard(workout)),
      ],
    );
  }

  Widget _buildWorkoutCard(Map<String, dynamic> workout) {
    final date = DateTime.parse(workout['created_at']);
    final formattedDate = DateFormat('MMM d, yyyy').format(date);
    final duration = workout['duration'] ?? 0;
    final durationText = duration > 0 
        ? '${(duration / 60).round()} min' 
        : 'Duration not recorded';

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: AppCard(
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: AppBorderRadius.buttonRadius,
            ),
            child: const Icon(
              Icons.fitness_center,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: AppSpacing.lg),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  workout['name'] ?? 'Workout',
                  style: AppTypography.heading3.copyWith(fontSize: 16),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  formattedDate,
                  style: AppTypography.body2,
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                durationText,
                style: AppTypography.body2.copyWith(
                  fontWeight: AppTypography.semiBold,
                  color: AppColors.primary,
                ),
              ),
              if (workout['rating'] != null)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(5, (index) {
                    return Icon(
                      index < workout['rating'] ? Icons.star : Icons.star_border,
                      size: 16,
                      color: AppColors.warning,
                    );
                  }),
                ),
            ],
          ),
        ],
      ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.trending_up,
              size: 40,
              color: Color(0xFF8B5CF6),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Workouts Yet',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Complete your first workout to see your progress here!',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: AppColorsTheme.textSecondary(context),
            ),
          ),
        ],
      ),
    );
  }
}