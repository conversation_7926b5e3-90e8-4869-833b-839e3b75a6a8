import 'package:flutter/material.dart';
import '../models/workout.dart';
import '../design_system/design_system.dart';

class WorkoutCard extends StatelessWidget {
  final Workout workout;
  final VoidCallback? onTap;
  final VoidCallback? onStart;

  const WorkoutCard({
    super.key,
    required this.workout,
    this.onTap,
    this.onStart,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.only(bottom: AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and status
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      workout.name,
                      style: AppTypography.heading3.copyWith(
                        color: AppColorsTheme.textPrimary(context),
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      workout.workoutSummary,
                      style: AppTypography.body2.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusChip(context),
            ],
          ),
          
          // AI Description (if available)
          if (workout.aiDescription != null && workout.aiDescription!.isNotEmpty) ...[
            const SizedBox(height: AppSpacing.md),
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: AppBorderRadius.chipRadius,
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.auto_awesome,
                    size: 16,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      workout.aiDescription!.length > 100
                          ? '${workout.aiDescription!.substring(0, 100)}...'
                          : workout.aiDescription!,
                      style: AppTypography.small.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: AppSpacing.md),

          // Exercise Preview
          if (workout.exercises.isNotEmpty) ...[
            Text(
              'Exercises:',
              style: AppTypography.buttonSmall.copyWith(
                color: AppColorsTheme.textSecondary(context),
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            ...workout.exercises.take(3).map((exercise) => Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.xs),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      '${exercise.exercise.name} • ${exercise.formattedReps}',
                      style: AppTypography.small.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                    ),
                  ),
                  if (exercise.exercise.equipment != null && 
                      exercise.exercise.equipment != 'None')
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.xs, 
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.darkBorder
                            : AppColors.grey200,
                        borderRadius: AppBorderRadius.chipRadius,
                      ),
                      child: Text(
                        exercise.exercise.equipment!,
                        style: AppTypography.small.copyWith(
                          color: AppColorsTheme.textSecondary(context),
                        ),
                      ),
                    ),
                ],
              ),
            )),
            if (workout.exercises.length > 3)
              Padding(
                padding: const EdgeInsets.only(top: AppSpacing.xs),
                child: Text(
                  '+${workout.exercises.length - 3} more exercises',
                  style: AppTypography.small.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],

          const SizedBox(height: AppSpacing.md),

          // Muscle Groups and Equipment
          Row(
            children: [
              if (workout.primaryMuscles.isNotEmpty) ...[
                Expanded(
                  child: _buildInfoChips(
                    context,
                    'Muscles',
                    workout.primaryMuscles.take(3).toList(),
                    Icons.fitness_center,
                    AppColors.success,
                  ),
                ),
              ],
              if (workout.equipmentNeeded.isNotEmpty) ...[
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _buildInfoChips(
                    context,
                    'Equipment',
                    workout.equipmentNeeded.take(2).toList(),
                    Icons.sports_gymnastics,
                    AppColors.warning,
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: AppSpacing.lg),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: AppButton(
                  text: workout.isActive ? 'Continue Workout' : 'Start Workout',
                  onPressed: onStart,
                  variant: workout.isActive 
                      ? AppButtonVariant.success 
                      : AppButtonVariant.primary,
                  size: AppButtonSize.medium,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              AppButton(
                text: 'View Details',
                onPressed: onTap,
                variant: AppButtonVariant.secondary,
                size: AppButtonSize.medium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color chipColor;
    String statusText;
    
    switch (workout.status) {
      case 'Active':
        chipColor = AppColors.success;
        statusText = 'Active';
        break;
      case 'Completed':
        chipColor = AppColors.primary;
        statusText = 'Completed';
        break;
      default:
        chipColor = AppColorsTheme.textSecondary(context);
        statusText = 'Draft';
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm, 
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.chipRadius,
        border: Border.all(
          color: chipColor.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        statusText,
        style: AppTypography.small.copyWith(
          fontWeight: AppTypography.semiBold,
          color: chipColor,
        ),
      ),
    );
  }

  Widget _buildInfoChips(
    BuildContext context,
    String label, 
    List<String> items, 
    IconData icon, 
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: AppSpacing.xs),
            Text(
              label,
              style: AppTypography.small.copyWith(
                fontWeight: AppTypography.semiBold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        Wrap(
          spacing: AppSpacing.xs,
          runSpacing: AppSpacing.xs,
          children: items.map((item) => Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.xs, 
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: AppBorderRadius.chipRadius,
              border: Border.all(
                color: color.withValues(alpha: 0.2),
              ),
            ),
            child: Text(
              item,
              style: AppTypography.small.copyWith(
                color: color,
                fontWeight: AppTypography.medium,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }
}